From 0a59aa440a4c125b81504c777b066ae4eb1f09f0 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 24 Sep 2019 04:15:17 -0700
Subject: [PATCH] xtensa: fix PR target/91880

Xtensa hwloop_optimize segfaults when zero overhead loop is about to be
inserted as the first instruction of the function.
Insert zero overhead loop instruction into new basic block before the
loop when basic block that precedes the loop is empty.

2019-09-26  Max Filippov  <<EMAIL>>
gcc/
	* config/xtensa/xtensa.c (hwloop_optimize): Insert zero overhead
	loop instruction into new basic block before the loop when basic
	block that precedes the loop is empty.

Signed-off-by: <PERSON>lippov <<EMAIL>>
Signed-off-by: <PERSON><PERSON> <<EMAIL>>
---
Backported from: r276166

 gcc/config/xtensa/xtensa.c | 5 +++--
 1 file changed, 3 insertions(+), 2 deletions(-)

diff --git a/gcc/config/xtensa/xtensa.c b/gcc/config/xtensa/xtensa.c
index ee5612441e2..2527468d57d 100644
--- a/gcc/config/xtensa/xtensa.c
+++ b/gcc/config/xtensa/xtensa.c
@@ -4235,7 +4235,9 @@ hwloop_optimize (hwloop_info loop)
 
   seq = get_insns ();
 
-  if (!single_succ_p (entry_bb) || vec_safe_length (loop->incoming) > 1)
+  entry_after = BB_END (entry_bb);
+  if (!single_succ_p (entry_bb) || vec_safe_length (loop->incoming) > 1
+      || !entry_after)
     {
       basic_block new_bb;
       edge e;
@@ -4256,7 +4258,6 @@ hwloop_optimize (hwloop_info loop)
     }
   else
     {
-      entry_after = BB_END (entry_bb);
       while (DEBUG_INSN_P (entry_after)
              || (NOTE_P (entry_after)
 		 && NOTE_KIND (entry_after) != NOTE_INSN_BASIC_BLOCK))
-- 
2.24.1

